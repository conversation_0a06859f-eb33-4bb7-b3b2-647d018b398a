/// Whisper GMS2 Extension - GML API接口
/// 为I Wanna游戏优化的实时语音识别系统

#region 核心API函数

/// @function whisper_init(model_path)
/// @description 初始化Whisper语音识别引擎
/// @param {string} model_path Whisper模型文件路径 (如: "models/ggml-small.bin")
/// @return {real} 成功返回1，失败返回0
function whisper_init(model_path) {
    return external_call(global.__whisper_init, model_path);
}

/// @function whisper_set_language(language)
/// @description 设置识别语言
/// @param {string} language 语言代码 ("zh"=中文, "en"=英文, "auto"=自动检测)
/// @return {real} 成功返回1，失败返回0
function whisper_set_language(language) {
    return external_call(global.__whisper_set_language, language);
}

/// @function whisper_start_recording()
/// @description 开始录音
/// @return {real} 成功返回1，失败返回0
function whisper_start_recording() {
    return external_call(global.__whisper_start_recording);
}

/// @function whisper_stop_recording()
/// @description 停止录音
/// @return {real} 成功返回1，失败返回0
function whisper_stop_recording() {
    return external_call(global.__whisper_stop_recording);
}

/// @function whisper_get_result()
/// @description 获取最新的识别结果文本
/// @return {string} 识别的文本内容
function whisper_get_result() {
    return external_call(global.__whisper_get_result);
}

/// @function whisper_get_confidence()
/// @description 获取最新识别结果的置信度
/// @return {real} 置信度值 (0.0-1.0)
function whisper_get_confidence() {
    return external_call(global.__whisper_get_confidence);
}

/// @function whisper_is_processing()
/// @description 检查是否正在处理语音
/// @return {real} 正在处理返回1，否则返回0
function whisper_is_processing() {
    return external_call(global.__whisper_is_processing);
}

/// @function whisper_cleanup()
/// @description 清理资源，释放内存
/// @return {real} 成功返回1
function whisper_cleanup() {
    return external_call(global.__whisper_cleanup);
}

#endregion

#region 高级配置函数

/// @function whisper_set_vad_params(threshold, freq_thold, silence_ms, keep_ms)
/// @description 设置语音活动检测参数
/// @param {real} threshold 语音检测阈值 (0.01-0.1, 默认0.01)
/// @param {real} freq_thold 频率阈值 (50-200, 默认100)
/// @param {real} silence_ms 静默时间毫秒 (1000-5000, 默认2000)
/// @param {real} keep_ms 是否保持毫秒精度 (0或1, 默认1)
/// @return {real} 成功返回1，失败返回0
function whisper_set_vad_params(threshold, freq_thold, silence_ms, keep_ms) {
    return external_call(global.__whisper_set_vad_params, threshold, freq_thold, silence_ms, keep_ms);
}

/// @function whisper_set_game_params(low_latency, process_interval, min_confidence)
/// @description 设置游戏优化参数
/// @param {real} low_latency 启用低延迟模式 (0或1, 默认1)
/// @param {real} process_interval 处理间隔毫秒 (5-50, 默认10)
/// @param {real} min_confidence 最小置信度 (0.3-0.9, 默认0.6)
/// @return {real} 成功返回1，失败返回0
function whisper_set_game_params(low_latency, process_interval, min_confidence) {
    return external_call(global.__whisper_set_game_params, low_latency, process_interval, min_confidence);
}

/// @function whisper_has_results()
/// @description 检查是否有新的识别结果
/// @return {real} 有结果返回1，否则返回0
function whisper_has_results() {
    return external_call(global.__whisper_has_results);
}

/// @function whisper_get_timestamp_begin()
/// @description 获取当前结果的开始时间戳
/// @return {real} 时间戳
function whisper_get_timestamp_begin() {
    return external_call(global.__whisper_get_timestamp_begin);
}

/// @function whisper_get_timestamp_end()
/// @description 获取当前结果的结束时间戳
/// @return {real} 时间戳
function whisper_get_timestamp_end() {
    return external_call(global.__whisper_get_timestamp_end);
}

#endregion

#region 扩展初始化

/// @function whisper_extension_init()
/// @description 初始化扩展，加载DLL并设置函数指针
/// @return {real} 成功返回1，失败返回0
function whisper_extension_init() {
    // 加载DLL
    var dll_path = working_directory + "whisper_gms2_extension.dll";
    global.__whisper_dll = external_define(dll_path, "whisper_init", dll_cdecl, ty_real, 1, ty_string);
    
    if (global.__whisper_dll < 0) {
        show_debug_message("Failed to load whisper_gms2_extension.dll");
        return 0;
    }
    
    // 定义所有外部函数
    global.__whisper_init = external_define(dll_path, "whisper_init", dll_cdecl, ty_string, 1, ty_string);
    global.__whisper_set_language = external_define(dll_path, "whisper_set_language", dll_cdecl, ty_real, 1, ty_string);
    global.__whisper_start_recording = external_define(dll_path, "whisper_start_recording", dll_cdecl, ty_real, 0);
    global.__whisper_stop_recording = external_define(dll_path, "whisper_stop_recording", dll_cdecl, ty_real, 0);
    global.__whisper_get_result = external_define(dll_path, "whisper_get_result", dll_cdecl, ty_string, 0);
    global.__whisper_get_confidence = external_define(dll_path, "whisper_get_confidence", dll_cdecl, ty_real, 0);
    global.__whisper_is_processing = external_define(dll_path, "whisper_is_processing", dll_cdecl, ty_real, 0);
    global.__whisper_cleanup = external_define(dll_path, "whisper_cleanup", dll_cdecl, ty_real, 0);
    global.__whisper_set_vad_params = external_define(dll_path, "whisper_set_vad_params", dll_cdecl, ty_real, 4, ty_real, ty_real, ty_real, ty_real);
    global.__whisper_set_game_params = external_define(dll_path, "whisper_set_game_params", dll_cdecl, ty_real, 3, ty_real, ty_real, ty_real);
    global.__whisper_has_results = external_define(dll_path, "whisper_has_results", dll_cdecl, ty_real, 0);
    global.__whisper_get_timestamp_begin = external_define(dll_path, "whisper_get_timestamp_begin", dll_cdecl, ty_real, 0);
    global.__whisper_get_timestamp_end = external_define(dll_path, "whisper_get_timestamp_end", dll_cdecl, ty_real, 0);
    
    show_debug_message("Whisper GMS2 Extension initialized successfully");
    return 1;
}

/// @function whisper_extension_cleanup()
/// @description 清理扩展资源
function whisper_extension_cleanup() {
    whisper_cleanup();
    external_free(global.__whisper_dll);
    show_debug_message("Whisper GMS2 Extension cleaned up");
}

#endregion

#region 便捷函数

/// @function whisper_quick_init(model_name, language)
/// @description 快速初始化，使用默认参数
/// @param {string} model_name 模型名称 ("tiny", "base", "small", "medium", "large")
/// @param {string} language 语言 ("zh", "en", "auto")，默认"zh"
/// @return {real} 成功返回1，失败返回0
function whisper_quick_init(model_name = "small", language = "zh") {
    // 构建模型路径
    var model_path = working_directory + "models/ggml-" + model_name + ".bin";
    
    // 初始化扩展
    if (!whisper_extension_init()) {
        return 0;
    }
    
    // 初始化Whisper
    if (!whisper_init(model_path)) {
        show_debug_message("Failed to initialize Whisper with model: " + model_path);
        return 0;
    }
    
    // 设置语言
    whisper_set_language(language);
    
    // 设置游戏优化参数
    whisper_set_game_params(1, 10, 0.6);  // 低延迟，10ms间隔，0.6置信度
    whisper_set_vad_params(0.01, 100, 2000, 1);  // 敏感VAD设置
    
    show_debug_message("Whisper quick init completed - Model: " + model_name + ", Language: " + language);
    return 1;
}

/// @function whisper_is_ready()
/// @description 检查Whisper是否准备就绪
/// @return {real} 准备就绪返回1，否则返回0
function whisper_is_ready() {
    return variable_global_exists("__whisper_dll") && global.__whisper_dll >= 0;
}

#endregion
